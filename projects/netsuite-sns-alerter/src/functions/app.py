import asyncio
from datetime import datetime
from typing import Any, Awaitable
from pydantic import ValidationError
from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.data_classes import SNSEvent, event_source
from aws_lambda_powertools.utilities.typing import LambdaContext

from src.context import Context
from src.models import SNSMessage

logger = Logger()

@logger.inject_lambda_context
@event_source(data_class=SNSEvent)
def handler(
    event: SNSEvent,
    _context: LambdaContext,
) -> dict[str, Any]:
    app_context = Context()

    tasks: list[Awaitable[None]] = []
    for record in event.records:
        try:
            message = SNSMessage.model_validate_json(record.sns.message)
        except ValidationError:
            logger.exception("Failed to validate SNS message", extra={ "sns_message": record.sns.message })
            continue

        logger.info("Processing SNS message", extra={ "sns_message": record.sns.message })
        timestamp = datetime.fromisoformat(record.sns.timestamp)
        for alert in message.alerts:
            # Create a coroutine for each alert
            task = app_context.alert_handler.process_alert(
                alert=alert,
                timestamp=timestamp,
            )
            tasks.append(task)

    async def main() -> None:
        # We can't use asyncio.gather(*tasks) here because our NetSuite
        # account has a concurrent request limit of 1 for each integration.
        for task in tasks:
            await task

    asyncio.run(main())

    return {"statusCode": 200, "body": "OK"}