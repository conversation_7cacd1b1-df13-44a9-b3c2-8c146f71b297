from aws_lambda_powertools import Logger

from src.models import <PERSON><PERSON>
from src.context import Context
from src.ns import SupportCase
from src.utils import generate_stable_eid


logger = Logger()


class InvalidStatusError(Exception): ...


class AlertHandler:
    def __init__(self, context: Context) -> None:
        self._context = context

    def process_alert(self, alert: Alert) -> None:
        if self._context.downtime_service.is_site_alerts_disabled(alert.site_id):
            return

        if alert.status == "firing":
            self._process_firing_alert(alert)

        elif alert.status == "resolved":
            self._process_resolved_alert(alert)

        else:
            raise InvalidStatusError(f"Unknown alert status: {alert.status}")

    def _process_firing_alert(self, alert: Alert) -> None:
        logger.debug("Processing firing alert", extra={"alert": alert.model_dump(mode="json")})
        eid = generate_stable_eid(alert)
        self._context.netsuite.create_support_case(
            SupportCase(
                eid=eid,
                title=alert.title,
                site_id=alert.site_id,
                company=self._context.netsuite_fields.NETSUITE_COMPANY,
                status=self._context.netsuite_fields.NETSUITE_STATUS_PENDING,
                origin=self._context.netsuite_fields.NETSUITE_ORIGIN_PLATFORM,
                category=self._context.netsuite_fields.NETSUITE_CATEGORY_L2_CAMERA_OFFLINE,
                priority=self._context.netsuite_fields.NETSUITE_PRIORITY_P3,
                product=self._context.netsuite_fields.NETSUITE_PRODUCT_EYECUE,
                incoming_message=alert.message,
            )
        )

    def _process_resolved_alert(self, alert: Alert) -> None:
        logger.debug("Processing resolved alert", extra={"alert": alert.model_dump(mode="json")})
        eid = generate_stable_eid(alert)
        self._context.netsuite.update_support_case(
            eid=eid,
            status=self._context.netsuite_fields.NETSUITE_STATUS_CLOSED,
        )
