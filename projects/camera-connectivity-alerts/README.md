# Camera Connectivity Alerts

This project provides an end‑to‑end solution for detecting and notifying on camera connectivity issues. It leverages Grafana, AWS SNS, AWS Lambda, DynamoDB, and Slack for notifications.

For detailed architecture and setup instructions, refer to the [Architecture Document](docs/Architecture.md).

## Getting Started

```bash
cd projects/camera-connectivity-alerts

# Install dependencies
pip install \
  -r requirements.txt \
  -r requirements-dev.txt

# Run the tests (you will need docker installed)
pytest
```

## Deployment

### Prerequisites

- AWS CLI configured with appropriate permissions.
- AWS SAM CLI installed (download from [here](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html)).

## Deploy the application

First we need to create the secrets in AWS Secrets Manager. You can do this using scripts provided in the `scripts` directory.

```bash
cat <<EOF > slack-webhook-url.txt
https://hooks.slack.com/services/your/slack/webhook
EOF
cat <<EOF > netsuite-credentials.json
{
  "customer_id": "13871",
  "account": "4818174_SB1",
  "consumer_key": "",
  "consumer_secret": "",
  "token_id": "",
  "token_secret": ""
}
EOF

export AWS_PROFILE=your-aws-profile
./scripts/create-secrets.sh

rm slack-webhook-url.txt netsuite-credentials.json
```

Then we can deploy the application using AWS SAM. The following parameters can be used with the `sam deploy` command:

| Parameter Name                 | Description                                                                    |
|--------------------------------|--------------------------------------------------------------------------------|
| `SlackWebHookSecretArn`        | The URL of the Slack webhook to send notifications to.                         |
| `NetSuiteCredentialsSecretArn` | The ARN of the AWS Secrets Manager secret containing the NetSuite credentials. |
| `LogEvent`                     | Whether to log the lambda events to CloudWatch.                                |
| `LogLevel`                     | The log level to use for the lambda functions.                                 |
| `Environment`                  | The environment to deploy to (e.g., `dev`, `prod`).

```bash
# Build the application
sam build
# Deploy the application (use the ARN of the secret created in the previous step)
sam deploy --parameter-overrides \
  SlackWebHookSecretArn=arn:aws:secretsmanager:us-east-1:123456789012:secret:camera-connectivity-alerts/slack-webhook-url-wUMzvj \
  NetSuiteCredentialsSecretArn=arn:aws:secretsmanager:us-east-1:123456789012:secret:camera-connectivity-alerts/netsuite-0u4RLy \
  LogEvent=true \
  LogLevel=INFO \
  Environment=dev
```
