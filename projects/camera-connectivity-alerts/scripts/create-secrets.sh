#!/usr/bin/env bash

set -euo pipefail

# Read Slack Webhook URL from a file
SLACK_WEBHOOK_URL=$(cat slack-webhook-url.txt)

# Read NetSuite credentials from a JSON file
NETSUITE_CREDENTIALS=$(cat netsuite-credentials.json)

# Create Slack Webhook URL secret
aws secretsmanager create-secret \
  --name "camera-connectivity-alerts/slack-webhook-url" \
  --description "Secret for Camera Connectivity Alerts Slack Webhook URL" \
  --secret-string "$SLACK_WEBHOOK_URL"

# Create NetSuite credentials secret
aws secretsmanager create-secret \
  --name "camera-connectivity-alerts/netsuite" \
  --description "Secret for NetSuite credentials" \
  --secret-string "$NETSUITE_CREDENTIALS"
