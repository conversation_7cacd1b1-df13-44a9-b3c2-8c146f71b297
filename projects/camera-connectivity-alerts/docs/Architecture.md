# Architecture

![Architecture Diagram](./diagrams/mermaid-sequence.svg)

---
## Deployment Components

1. **AWS CloudFormation**: Deploys core components (SNS, Lambda, DynamoDB)
2. **Terraform**: Manages IAM user for Grafana integration

## Flow
1. **Alert Generation**:
   Grafana monitors camera connectivity and sends alerts to AWS SNS.
   
2. **Event Processing & Deduplication**:
   The **Primary Lambda Function** is triggered by SNS events. It processes each alert and performs an atomic conditional write to a DynamoDB table.
   - If a new record is created (i.e. the `put_item` call succeeds), the record is inserted with the `acknowledged` flag set to `true` and a Slack notification is sent.
   - If the record already exists (i.e. the condition fails), the function skips sending a duplicate notification.

3. **Record Maintenance**:
   The DynamoDB table uses TTL for automatic expiration of records after 7 days.

## Components

### Primary Lambda Function

- **Processing**:
  Processes alerts from SNS, writes alert records to DynamoDB, and sends Slack notifications.

- **Deduplication**:
  Uses an atomic conditional write (with a `put_item` ConditionExpression) to ensure that a Slack notification is sent only when a new alert record is inserted.

- **Efficiency**:
  Combines processing and notification in a single function to reduce race conditions and simplify the overall flow.

### DynamoDB Table

- **Alert Records**:
  Stores camera alert records using a composite key (`SiteId`, `CameraId`).

## Grafana Setup

1. **Create a Contact Point**:
   In Grafana, create a new contact point with AWS SNS integration.

2. **Configure Credentials**:
   Use the access key and secret key from Terraform outputs.

3. **Alert Rules**:
   Configure alerting rules to detect camera connectivity issues.

4. **SNS Topic ARN**:
   Set the SNS topic ARN from CloudFormation outputs.

### Grafana SNS Integration

#### 1. Create an SNS contact point in Grafana

Use a custom Go template for the Message field to output valid JSON. Example template:

```
{
  "externalURL": "{{ .ExternalURL }}",
  "alerts": [
    {{- range $index, $alert := .Alerts }}
    {{- if $index }},{{ end }}
    {
      "status": "{{ $alert.Status }}",
      "labels": {
         {{- $first := true }}
         {{- range $key, $value := .Labels }}
           {{- if not $first }}, {{ end }}
           "{{ $key }}": "{{ $value }}"
           {{- $first = false }}
         {{- end }}
      }
    }
    {{- end }}
  ]
}
```

#### 2. IAM Permissions:

- Ensure the Grafana IAM user has permission to publish to the SNS topic.
- Lambda functions need the appropriate policies to read/write to DynamoDB and send notifications.
