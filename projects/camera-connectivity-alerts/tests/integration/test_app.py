from http import HTTPStatus
from unittest.mock import ANY, patch

import pytest
from pynamodb.exceptions import DeleteError, PutError

from src.models import Camera<PERSON>tatus, SNSMessage, alert_to_camera_status
from tests.util import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SNSEventFactory


def test_handler_process_single_alert(
    lambda_handler: <PERSON><PERSON><PERSON>ambda<PERSON><PERSON><PERSON>,
) -> None:
    """Test the Lambda handler function."""
    alert = SNSEventFactory.create_alert(status="firing")
    sns_event = SNSEventFactory.create_event(alerts=[alert])

    # Call the handler function
    response = lambda_handler(sns_event)

    assert response["statusCode"] == HTTPStatus.OK
    assert response["body"] == "OK"

    # Check if the camera status was created in DynamoDB
    camera_status = CameraStatus.get(
        hash_key="fm-mcd-aus-2032",
        range_key="camera10",
    )
    assert camera_status is not None
    assert camera_status.SiteId == "fm-mcd-aus-2032"
    assert camera_status.CameraId == "camera10"
    assert camera_status.Host == "************"


def test_handler_resolve_alert(lambda_handler: <PERSON><PERSON><PERSON>ambdaH<PERSON><PERSON>) -> None:
    """Test the Lambda handler function."""
    message1 = SNSMessage(alerts=[SNSEventFactory.create_alert(status="firing")])
    record1 = SNSEventFactory.create_event_record(message1)
    # Fix the timestamp to a specific value
    record1._data["Sns"]["Timestamp"] = "2025-01-01T12:00:00.000Z"  # noqa: SLF001
    sns_event1 = SNSEventFactory.create_event(records=[record1])

    message2 = SNSMessage(alerts=[SNSEventFactory.create_alert(status="resolved")])
    record2 = SNSEventFactory.create_event_record(message2)
    # Fix the timestamp to a specific value 1 hour later
    record2._data["Sns"]["Timestamp"] = "2025-01-01T13:00:00.000Z"  # noqa: SLF001
    sns_event2 = SNSEventFactory.create_event(records=[record2])

    # Call the handler function with the firing alert
    with (
        patch("src.functions.app.slack_notify") as mock_slack_notify,
        patch("src.functions.app.get_outage_ticket", return_value={"id": "ticket-id"}),
    ):
        response1 = lambda_handler(sns_event1)

    assert response1["statusCode"] == HTTPStatus.OK
    assert CameraStatus.count() == 1
    mock_slack_notify.assert_called_once_with(
        ":orange_alert: Camera *camera10* (`************`) at site "
        "`fm-mcd-aus-2032` has gone offline. "
        "View the ticket here: <https://mock-account.app.netsuite.com/app/crm/support/supportcase.nl?id=ticket-id|NetSuite Ticket>",  # noqa: E501
        ANY,
    )

    # Call the handler function with the resolved alert
    with (
        patch("src.functions.app.slack_notify") as mock_slack_notify,
        patch("src.functions.app.get_outage_ticket", return_value={"id": "ticket-id"}),
    ):
        response2 = lambda_handler(sns_event2)

    assert response2["statusCode"] == HTTPStatus.OK
    assert CameraStatus.count() == 0
    mock_slack_notify.assert_called_once_with(
        ":green-alert: Camera *camera10* (`************`) at site "
        "`fm-mcd-aus-2032` is back online after 1h 0m. "
        "View the ticket here: <https://mock-account.app.netsuite.com/app/crm/support/supportcase.nl?id=ticket-id|NetSuite Ticket>",  # noqa: E501
        ANY,
    )


def test_handler_duplicate_firing_message(lambda_handler: MockedLambdaHandler) -> None:
    """Test the Lambda handler function with duplicate messages."""
    alert = SNSEventFactory.create_alert(status="firing")
    sns_event = SNSEventFactory.create_event(alerts=[alert])

    # Call the handler function with the firing alert
    with patch("src.functions.app.slack_notify") as mock_slack_notify:
        response1 = lambda_handler(sns_event)

    assert response1["statusCode"] == HTTPStatus.OK
    assert CameraStatus.count() == 1
    mock_slack_notify.assert_called_once()

    # Call the handler function again with the same alert
    with patch("src.functions.app.slack_notify") as mock_slack_notify:
        response1 = lambda_handler(sns_event)

    assert response1["statusCode"] == HTTPStatus.OK
    assert CameraStatus.count() == 1
    mock_slack_notify.assert_not_called()


def test_handler_duplicate_resolved_message(
    lambda_handler: MockedLambdaHandler,
) -> None:
    """Test the Lambda handler function with duplicate messages."""
    alert = SNSEventFactory.create_alert(status="resolved")
    sns_event = SNSEventFactory.create_event(alerts=[alert])

    # Set the status to "firing" to simulate the camera
    # already being offline in the database
    camera_status = alert_to_camera_status(alert)
    camera_status.Status = "firing"
    camera_status.save()

    # Call the handler function with the resolved alert
    with patch("src.functions.app.slack_notify") as mock_slack_notify:
        response1 = lambda_handler(sns_event)

    assert response1["statusCode"] == HTTPStatus.OK
    assert CameraStatus.count() == 0
    mock_slack_notify.assert_called_once()

    with patch("src.functions.app.slack_notify") as mock_slack_notify:
        response2 = lambda_handler(sns_event)

    assert response2["statusCode"] == HTTPStatus.OK
    assert CameraStatus.count() == 0
    mock_slack_notify.assert_not_called()


def test_invalid_sns_message(
    lambda_handler: MockedLambdaHandler,
    caplog: pytest.LogCaptureFixture,
) -> None:
    """Test the Lambda handler function with an invalid SNS message."""
    alert = SNSEventFactory.create_alert(status="firing")
    alert.status = "invalid"  # type: ignore[assignment] # Set an invalid status
    sns_event = SNSEventFactory.create_event(alerts=[alert])

    # Call the handler function with the invalid SNS message
    response = lambda_handler(sns_event)

    assert response["statusCode"] == HTTPStatus.OK
    assert caplog.record_tuples == [
        (
            "service_undefined",
            40,
            "Failed to validate SNS message",
        )
    ]


def test_pynamodb_exception_firing(
    lambda_handler: MockedLambdaHandler,
    caplog: pytest.LogCaptureFixture,
) -> None:
    """Test the Lambda handler function with a PynamoDB exception."""
    alert = SNSEventFactory.create_alert(status="firing")
    sns_event = SNSEventFactory.create_event(alerts=[alert])

    # Simulate a PynamoDB exception
    with patch("src.functions.app.CameraStatus.save") as mock_save:
        mock_save.side_effect = PutError()

        # Call the handler function with the firing alert
        response = lambda_handler(sns_event)

    assert response["statusCode"] == HTTPStatus.OK
    assert caplog.record_tuples == [
        (
            "service_undefined",
            40,
            "PynamoDB exception occurred",
        )
    ]


def test_pynamodb_exception_resolved(
    lambda_handler: MockedLambdaHandler,
    caplog: pytest.LogCaptureFixture,
) -> None:
    """Test the Lambda handler function with a PynamoDB exception."""
    alert = SNSEventFactory.create_alert(status="resolved")
    sns_event = SNSEventFactory.create_event(alerts=[alert])

    # Simulate a PynamoDB exception
    with patch("src.functions.app.CameraStatus.delete") as mock_delete:
        mock_delete.side_effect = DeleteError()

        # Call the handler function with the resolved alert
        response = lambda_handler(sns_event)

    assert response["statusCode"] == HTTPStatus.OK
    assert caplog.record_tuples == [
        (
            "service_undefined",
            40,
            "PynamoDB exception occurred",
        )
    ]
