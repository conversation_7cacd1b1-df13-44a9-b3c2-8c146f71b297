from src.models import SNSMessage


def test_sns_message_model() -> None:
    message = """
        {
            "receiver": "Platform camera connectivity SNS (dev)",
            "status": "resolved",
            "externalURL": "https://grafana.infra.fingermark.tech/",
            "alerts": [
                {
                    "status": "resolved",
                    "labels": {
                        "__alert_rule_uid__": "fehkx0h5kbpxce",
                        "__grafana_autogenerated__": "true",
                        "__grafana_receiver__": "Platform camera connectivity SNS (dev)",
                        "__name__": "eyecue_camera_ping_alive_status",
                        "alertname": "Camera Offline Alerts",
                        "camera_id": "camera11",
                        "grafana_folder": "INFRA",
                        "host": "************",
                        "instance": "camera-metrics-exporter.monitoring.svc.cluster.local:8080",
                        "job": "camera-metrics-exporter",
                        "site_id": "fm-mcd-aus-2125"
                    }
                }
            ]
        }
"""  # noqa: E501

    # Should not raise an exception
    SNSMessage.model_validate_json(message)
