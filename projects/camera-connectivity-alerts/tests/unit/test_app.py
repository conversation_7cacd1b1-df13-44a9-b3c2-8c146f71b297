from unittest.mock import patch

import pytest
from netsuite.exceptions import NetsuiteAPIRequestError
from pynamodb.exceptions import PutError

from src.functions import app


def test_slack_notify_with_valid_webhook_url() -> None:
    """Test slack_notify when SLACK_WEBHOOK_URL is set."""
    message = "Test Slack notification"
    webhook_url = (
        "*****************************************************************************"
    )

    with patch("src.functions.app.http.request") as mock_request:
        app.slack_notify(message, webhook_url)

    mock_request.assert_called_once_with(
        "POST",
        webhook_url,
        headers={"Content-Type": "application/json"},
        body=f'{{"text": "{message}"}}',
    )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ("exception", "message"),
    [
        (Exception("Test exception"), "Unexpected error occurred"),
        (PutError(), "PynamoDB exception occurred"),
        (
            NetsuiteAPIRequestError(400, "Bad Request"),
            "Netsuite API request error occurred",
        ),
    ],
)
async def test_exception_handling(
    exception: Exception, message: str, caplog: pytest.LogCaptureFixture
) -> None:
    async with app.exception_handler():
        raise exception

    assert caplog.record_tuples == [
        (
            "service_undefined",
            40,
            message,
        )
    ]
    assert caplog.records[0].exc_info == (
        type(exception),
        exception,
        exception.__traceback__,
    )
