from collections.abc import Callable
from typing import Any, Literal, overload

from aws_lambda_powertools.utilities.data_classes import SNSEvent
from aws_lambda_powertools.utilities.data_classes.sns_event import SNSEventRecord

from src.models import Alert, Labels, SNSMessage

MockedLambdaHandler = Callable[[Any], dict[str, Any]]


class SNSEventFactory:
    @staticmethod
    def create_alert(
        status: Literal["firing", "resolved"] = "firing",
        labels: Labels | None = None,
    ) -> Alert:
        if labels is None:
            labels = Labels(
                alertname="Camera Offline Alerts",
                site_id="fm-mcd-aus-2032",
                camera_id="camera10",
                host="************",
            )

        return Alert(
            labels=labels,
            status=status,
        )

    @staticmethod
    def create_event_record(
        message: SNSMessage,
    ) -> SNSEventRecord:
        return SNSEventRecord(
            {
                "EventSource": "aws:sns",
                "EventSubscriptionArn": (
                    "arn:aws:sns:us-east-1:123456789012:MyTopic:12345678-1234-1234-1234-123456789012"
                ),
                "Sns": {
                    "Type": "Notification",
                    "MessageId": "12345678-1234-1234-1234-123456789012",
                    "TopicArn": "arn:aws:sns:us-east-1:123456789012:MyTopic",
                    "Subject": "Test Subject",
                    "Message": message.model_dump_json(),
                    "Timestamp": "2025-01-01T12:00:00.000Z",
                    "SignatureVersion": "1",
                    "Signature": "",
                    "SigningCertUrl": "",
                    "UnsubscribeUrl": "",
                },
            }
        )

    @staticmethod
    def create_event_records(
        messages: list[SNSMessage],
    ) -> list[SNSEventRecord]:
        return [SNSEventFactory.create_event_record(message) for message in messages]

    @overload
    @staticmethod
    def create_event(
        *,
        alerts: list[Alert],
        messages: None = None,
        records: None = None,
    ) -> SNSEvent: ...

    @overload
    @staticmethod
    def create_event(
        *,
        alerts: None = None,
        messages: list[SNSMessage],
        records: None = None,
    ) -> SNSEvent: ...

    @overload
    @staticmethod
    def create_event(
        *,
        alerts: None = None,
        messages: None = None,
        records: list[SNSEventRecord],
    ) -> SNSEvent: ...

    @staticmethod
    def create_event(
        *,
        alerts: list[Alert] | None = None,
        messages: list[SNSMessage] | None = None,
        records: list[SNSEventRecord] | None = None,
    ) -> SNSEvent:
        """Create a mock SNS event."""
        records = records or []

        if alerts is not None:
            message = SNSMessage(alerts=alerts)
            record = SNSEventFactory.create_event_record(message)
            records = [record]

        if messages is not None:
            records = SNSEventFactory.create_event_records(messages)

        return SNSEvent({"Records": records})
