import warnings
from collections.abc import Iterable
from typing import TYPE_CHECKING, Any
from unittest.mock import AsyncMock, patch

import pytest
from aws_lambda_powertools.utilities.typing import LambdaContext
from localstack_utils.localstack import startup_localstack, stop_localstack
from netsuite import Config, TokenAuth
from pynamodb.exceptions import TableError

from src.context import Context, Fields
from src.functions.app import handler
from src.models import CameraStatus
from src.ns import SupportCaseField
from tests.util import MockedLambdaHandler

if TYPE_CHECKING:
    from pynamodb.models import MetaProtocol

LOCALSTACK_ENDPOINT_URL = "http://localhost:4566"


@pytest.fixture(autouse=True, scope="session")
def setup_localstack() -> Iterable[None]:  # pragma: no cover
    """Start and stop LocalStack for the entire test session."""
    localstack_already_running = False

    try:
        startup_localstack()
    except TypeError as e:
        # NOTE:(<PERSON>): This error is raised when LocalStack is already running
        # so we only want to raise it if it's not this error.
        if str(e) != "exceptions must derive from BaseException":
            raise e

        localstack_already_running = True
        warnings.warn(
            UserWarning(
                "LocalStack is already running and will not "
                "be stopped at the end of this test session."
            ),
            stacklevel=2,
        )

    yield

    # Only stop LocalStack if it was started in this session
    if not localstack_already_running:
        stop_localstack()


@pytest.fixture(autouse=True)
def camera_status_table(
    request: pytest.FixtureRequest,
) -> Iterable[type[CameraStatus]]:  # pragma: no cover
    # Give each test a unique table name
    table_name = f"pytest-{
        request.node.name.translate(
            # A roundabout way to convert the test name to a valid table name
            str.maketrans({'_': '-', ' ': '-', '[': '-', ']': '-'})
        )
    }"

    model_meta_class: MetaProtocol = CameraStatus.Meta  # type: ignore[assignment]

    model_meta_class.host = LOCALSTACK_ENDPOINT_URL
    model_meta_class.region = "us-east-1"
    model_meta_class.table_name = table_name
    model_meta_class.read_capacity_units = 1
    model_meta_class.write_capacity_units = 1
    model_meta_class.aws_access_key_id = "any"
    model_meta_class.aws_secret_access_key = "any"
    model_meta_class.aws_session_token = "any"

    try:
        CameraStatus.create_table(wait=True)
    except TableError as e:
        if "ResourceInUseException" not in e.msg:
            raise

        # Table already exists so we recreate it
        CameraStatus.delete_table()
        CameraStatus.create_table(wait=True)

    yield CameraStatus

    CameraStatus.delete_table()


@pytest.fixture
def lambda_context() -> LambdaContext:
    """Fixture for a Lambda context.

    Taken from here:
    https://docs.powertools.aws.dev/lambda/python/3.8.0/core/logger/#testing-your-code

    """

    class TestLambdaContext(LambdaContext):
        _function_name: str = "test"
        _memory_limit_in_mb: int = 128
        _invoked_function_arn: str = "arn:aws:lambda:eu-west-1:*********:function:test"
        _aws_request_id: str = "52fdfc07-2182-154f-163f-5f0f9a621d72"

    return TestLambdaContext()


@pytest.fixture
def lambda_execution_context() -> Context:
    fields = Fields(
        NETSUITE_COMPANY=SupportCaseField(name="company", id="mock company id")
    )

    context = Context(
        slack_webhook_url="https://hooks.slack.com/services/*********/*********/XXXXXXXXXXXXXXXX",
        netsuite_config=Config(
            account="mock_account",
            auth=TokenAuth(
                consumer_key="mock_consumer_key",
                consumer_secret="mock_consumer_secret",
                token_id="mock_token_id",
                token_secret="mock_token_secret",
            ),
        ),
        netsuite_fields=fields,
    )

    context.netsuite = AsyncMock()

    return context


@pytest.fixture
def lambda_handler(
    lambda_context: LambdaContext,
    lambda_execution_context: Context,
) -> Iterable[MockedLambdaHandler]:
    """Fixture for the Lambda handler function.

    This is a mock of the handler function that can be used in tests.
    """

    def mocked_handler(event: Any) -> dict[str, Any]:
        return handler(event, lambda_context)  # type: ignore[no-any-return]

    with patch(
        "src.functions.app.get_execution_context",
        return_value=lambda_execution_context,
    ):
        yield mocked_handler
