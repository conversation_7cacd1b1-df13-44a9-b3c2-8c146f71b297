[tool.ruff]
target-version = "py313"
line-length = 88
lint.extend-select = ["ALL"]
lint.ignore = [
    "A005",
    "ANN002",
    "ANN003",
    "ANN401",
    "COM812",
    "D1",
    "D203",
    "D211",
    "D213",
    "D407",
    "EM",
    "ERA",
    "ISC001",
    "S",
    "TRY",
    "TD003",
    "FIX002",
]

[tool.ruff.lint.per-file-ignores]
"src/functions/**/*.py" = [
  "INP001",
]

"tests/**/*.py" = [
    "INP001",
    "ANN201",
]

[tool.ruff.lint.flake8-tidy-imports]
ban-relative-imports = "all"

[tool.mypy]
strict = true
files = [
    "src",
    "tests",
]
ignore_missing_imports = true
explicit_package_bases = true
check_untyped_defs = true

[[tool.mypy.overrides]]
module = 'tests.*'
disable_error_code = "var-annotated"

[tool.pytest.ini_options]
addopts = "--cov=src --cov=tests --cov-report term --cov-config=pyproject.toml"
required_plugins = ["pytest-cov"]
testpaths = ["tests"]
filterwarnings = [
    "ignore:datetime.datetime.utcnow.*:DeprecationWarning"  # boto3
]
asyncio_default_fixture_loop_scope="function"

[tool.coverage.run]
branch = true
omit = [
    "src/ns.py",
    "tests/util.py",
]

[tool.coverage.report]
skip_empty = true
fail_under = 100
sort = "cover"
show_missing = true
omit = [
    "*/__main__.py",
]
exclude_also = [
    "if TYPE_CHECKING:",
    "if __name__ == .__main__.:"
]
