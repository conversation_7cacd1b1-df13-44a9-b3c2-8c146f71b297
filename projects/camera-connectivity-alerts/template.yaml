AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  Camera Connectivity Alerts - Monitors cameras and sends notifications.

Globals:
  Function:
    Runtime: python3.13
    Timeout: 300
    MemorySize: 128
    Environment:
      Variables:
        POWERTOOLS_SERVICE_NAME: !Ref AWS::StackName
        POWERTOOLS_LOG_LEVEL: !Ref LogLevel
        POWERTOOLS_LOGGER_LOG_EVENT: !Ref LogEvent
    Tags:
      Environment: !Ref Environment
      Squad: platform

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - prod
    Description: Deployment environment

  LogLevel:
    Type: String
    Default: INFO
    AllowedValues:
      - DEBUG
      - INFO
      - WARNING
      - ERROR
      - CRITICAL
    Description: Log level for the Lambda function

  LogEvent:
    Type: String
    Default: "false"
    AllowedValues:
      - "true"
      - "false"
    Description: Log event for the Lambda function

  SlackWebHookSecretArn:
    Type: String
    Description: ARN of the Secrets Manager secret that stores the Slack webhook URL
    Default: arn:aws:secretsmanager:ap-southeast-2:979525481730:secret:camera-connectivity-alerts/slack-webhook-url-wUMzvj

  NetSuiteSecretArn:
    Type: String
    Description: ARN of the Secrets Manager secret that stores NetSuite credentials
    Default: arn:aws:secretsmanager:ap-southeast-2:979525481730:secret:camera-connectivity-alerts/netsuite-0u4RLy

  AlarmSNSArn:
    Type: String
    Description: ARN of the SNS topic to notify when the ProcessAlertsFunction fails

Conditions:
  AlarmSNSEnabled: !Not [!Equals [!Ref AlarmSNSArn, ""]]

Resources:
  # SNS Topic for receiving alerts
  AlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      DisplayName: !Sub "Camera Connectivity Alerts (${Environment})"
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Squad
          Value: platform

  # Single Lambda function to process alerts and send notifications
  ProcessAlertsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "camera-connectivity-alerts-${Environment}"
      Handler: src.functions.app.handler
      CodeUri: .
      Description: "Processes alerts for camera connectivity issues and sends notifications"
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          CAMERA_ALERT_TABLE: !Ref CameraAlertTable
          SLACK_WEBHOOK_URL: !Sub "{{resolve:secretsmanager:${SlackWebHookSecretArn}:SecretString}}"
          NETSUITE_CUSTOMER_ID: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:customer_id}}"
          NETSUITE_ACCOUNT: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:account}}"
          NETSUITE_CONSUMER_KEY: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:consumer_key}}"
          NETSUITE_CONSUMER_SECRET: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:consumer_secret}}"
          NETSUITE_TOKEN_ID: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:token_id}}"
          NETSUITE_TOKEN_SECRET: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:token_secret}}"
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref CameraAlertTable
      Events:
        SNSEvent:
          Type: SNS
          Properties:
            Topic: !Ref AlertsTopic

  # DynamoDB table to store camera alerts
  CameraAlertTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "camera-connectivity-alerts-${Environment}"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: SiteId
          AttributeType: S
        - AttributeName: CameraId
          AttributeType: S
      KeySchema:
        - AttributeName: SiteId
          KeyType: HASH
        - AttributeName: CameraId
          KeyType: RANGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Squad
          Value: platform

  # ---- Process Alerts Function Alarms ----

  ProcessAlertsFunctionAlarm:
    Type: AWS::CloudWatch::Alarm
    Condition: AlarmSNSEnabled
    Properties:
      AlarmDescription: "Alarm when the ProcessAlertsFunction fails"
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 1
      Threshold: 0
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref ProcessAlertsFunction
      AlarmActions:
        - !Ref AlarmSNSArn
      OKActions:
        - !Ref AlarmSNSArn

Outputs:
  SNSTopicArn:
    Description: "SNS Topic ARN for alerts"
    Value: !Ref AlertsTopic

  CameraAlertTableName:
    Description: "DynamoDB table name"
    Value: !Ref CameraAlertTable
