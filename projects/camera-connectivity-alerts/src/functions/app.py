import asyncio
import contextlib
from collections.abc import Async<PERSON>tera<PERSON>, Awaitable
from datetime import datetime
from typing import Any

import urllib3
from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.data_classes import SNSEvent, event_source
from aws_lambda_powertools.utilities.typing import LambdaContext
from netsuite.client import <PERSON>SuiteRestApi
from netsuite.exceptions import NetsuiteAPIRequestError
from pydantic import ValidationError
from pynamodb.exceptions import DeleteError, PutError, PynamoDBException

from src import messages
from src.context import Context, Fields
from src.models import Alert, CameraStatus, SNSMessage, alert_to_camera_status
from src.ns import (
    SupportCase,
    create_support_case,
    get_support_case,
    update_support_case,
)
from src.utils import create_eid

logger = Logger()

http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=2.0, read=2.0))


def get_execution_context() -> Context:  # pragma: no cover
    """Get the execution context for the Lambda (mainly here for testing purposes)."""
    return Context()


@logger.inject_lambda_context
@event_source(data_class=SNSEvent)  # type: ignore[misc]
def handler(
    event: SNSEvent,
    _context: LambdaContext,
) -> dict[str, Any]:
    execution_context: Context = get_execution_context()

    tasks: list[Awaitable[None]] = []
    for record in event.records:
        try:
            message = SNSMessage.model_validate_json(record.sns.message)
        except ValidationError:
            logger.exception(
                "Failed to validate SNS message",
                extra={
                    "sns_message": record.sns.message,
                },
            )
            continue

        timestamp = datetime.fromisoformat(record.sns.timestamp)
        for alert in message.alerts:
            # Create a coroutine for each alert
            task = process_alert(
                alert=alert,
                timestamp=timestamp,
                execution_context=execution_context,
            )
            tasks.append(task)

    async def main() -> None:
        # We can't use asyncio.gather(*tasks) here because our NetSuite
        # account has a concurrent request limit of 1 for each integration.
        for task in tasks:
            await task

    asyncio.run(main())

    return {"statusCode": 200, "body": "OK"}


@contextlib.asynccontextmanager
async def exception_handler() -> AsyncIterator[None]:
    try:
        yield
    except Exception as e:
        if isinstance(e, PynamoDBException):
            logger.exception(
                "PynamoDB exception occurred",
                extra={
                    "response_code": e.cause_response_code,
                    "response_message": e.cause_response_message,
                },
            )
        elif isinstance(e, NetsuiteAPIRequestError):
            logger.exception(
                "Netsuite API request error occurred",
                extra={
                    "status_code": e.status_code,
                    "response_text": e.response_text,
                },
            )
        else:
            logger.exception("Unexpected error occurred")


@exception_handler()
async def process_alert(
    alert: Alert,
    timestamp: datetime,
    execution_context: Context,
) -> None:
    logger.debug("Processing alert", extra={"alert": alert.model_dump(mode="json")})
    camera_status = alert_to_camera_status(alert, CreatedAt=timestamp)

    netsuite, netsuite_config, netsuite_fields, slack_webhook_url = (
        execution_context.netsuite,
        execution_context.netsuite_config,
        execution_context.netsuite_fields,
        execution_context.slack_webhook_url,
    )

    if alert.status == "firing":
        try:
            camera_status.save(
                CameraStatus.SiteId.does_not_exist()
                & CameraStatus.CameraId.does_not_exist()
            )
        except PutError as e:
            # If the item already exists, we can assume it was already created
            if e.cause_response_code != "ConditionalCheckFailedException":
                raise
            return

        logger.info("Alert is firing", extra={"alert": alert.model_dump(mode="json")})
        await raise_outage_ticket(
            camera_status=camera_status,
            netsuite=netsuite,
            fields=netsuite_fields,
        )

        if slack_webhook_url is not None:  # pragma: no branch
            ticket = await get_outage_ticket(
                netsuite=netsuite, camera_status=camera_status
            )

            message = messages.slack_offline_message(
                camera_status=camera_status,
                netsuite_account=netsuite_config.account_slugified,
                ticket_id=ticket["id"],
            )
            slack_notify(message, slack_webhook_url)

    if alert.status == "resolved":
        try:
            deleted_camera = camera_status.delete(
                CameraStatus.SiteId.exists() & CameraStatus.CameraId.exists()
            )
        except DeleteError as e:
            # If the item does not exist, we can assume it was already deleted
            if e.cause_response_code != "ConditionalCheckFailedException":
                raise
            return

        logger.info("Alert is resolved", extra={"alert": alert.model_dump(mode="json")})
        camera_status.CreatedAt = datetime.fromisoformat(deleted_camera["CreatedAt"])
        await close_outage_ticket(
            camera_status=camera_status,
            netsuite=netsuite,
            fields=netsuite_fields,
        )

        if slack_webhook_url is not None:  # pragma: no branch
            ticket = await get_outage_ticket(
                camera_status=camera_status,
                netsuite=netsuite,
            )

            duration = timestamp - camera_status.CreatedAt
            message = messages.slack_online_message(
                camera_status=camera_status,
                duration=duration,
                netsuite_account=netsuite_config.account_slugified,
                ticket_id=ticket["id"],
            )
            slack_notify(message, slack_webhook_url)


def slack_notify(message: str, slack_webhook_url: str) -> None:
    """Send a Slack notification."""
    http.request(
        "POST",
        slack_webhook_url,
        headers={"Content-Type": "application/json"},
        body=f'{{"text": "{message}"}}',
    )


async def raise_outage_ticket(
    camera_status: CameraStatus, netsuite: NetSuiteRestApi, fields: Fields
) -> None:
    """Raise an outage ticket in Netsuite for the specified camera."""
    eid = create_eid(
        site_id=camera_status.SiteId,
        camera_id=camera_status.CameraId,
        created_at=camera_status.CreatedAt,
    )

    logger.debug("Creating support case with eid %s", eid)
    await create_support_case(
        netsuite,
        SupportCase(
            eid=eid,
            title=f"[CAMERA OFFLINE] {camera_status.CameraId} ({camera_status.SiteId})",
            site_id=camera_status.SiteId,
            company=fields.NETSUITE_COMPANY,
            status=fields.NETSUITE_STATUS_PENDING,
            origin=fields.NETSUITE_ORIGIN_PLATFORM,
            category=fields.NETSUITE_CATEGORY_L2_CAMERA_OFFLINE,
            priority=fields.NETSUITE_PRIORITY_P3,
            product=fields.NETSUITE_PRODUCT_EYECUE,
            incoming_message=messages.netsuite_message(),
        ),
    )


async def get_outage_ticket(
    camera_status: CameraStatus,
    netsuite: NetSuiteRestApi,
) -> dict[str, Any]:
    """Get the outage ticket in Netsuite for the specified camera."""
    eid = create_eid(
        site_id=camera_status.SiteId,
        camera_id=camera_status.CameraId,
        created_at=camera_status.CreatedAt,
    )

    logger.debug("Getting support case with eid %s", eid)
    return await get_support_case(
        ns=netsuite,
        eid=eid,
    )


async def close_outage_ticket(
    camera_status: CameraStatus,
    netsuite: NetSuiteRestApi,
    fields: Fields,
) -> None:
    """Close the outage ticket in Netsuite for the specified camera."""
    eid = create_eid(
        site_id=camera_status.SiteId,
        camera_id=camera_status.CameraId,
        created_at=camera_status.CreatedAt,
    )

    logger.debug("Closing support case with eid %s", eid)
    await update_support_case(
        ns=netsuite,
        eid=eid,
        status=fields.NETSUITE_STATUS_CLOSED,
    )
