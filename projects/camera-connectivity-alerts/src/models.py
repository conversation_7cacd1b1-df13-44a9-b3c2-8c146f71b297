import os
from datetime import UTC, datetime
from typing import Any, Literal, NotRequired, TypedDict, Unpack

from pydantic import BaseModel, Field
from pynamodb.attributes import UnicodeAttribute, UTCDateTimeAttribute
from pynamodb.expressions.condition import Condition
from pynamodb.models import Model as PynamoModel

from src.utils import dynamo_json_to_object

# ========== Pydantic Models ==========
# These models are used for validating the incoming SNS message from Grafana.
# Some fields have been omitted for brevity.


class Labels(BaseModel):
    """A Pydantic model for the labels in the alert."""

    alertname: str = Field(
        description="Name of the alert",
        examples=["Camera Offline Alerts"],
    )
    site_id: str = Field(
        description="ID of the site",
        examples=["fm-mcd-aus-2032"],
    )
    camera_id: str = Field(
        description="ID of the camera",
        examples=["camera10"],
    )
    host: str = Field(
        description="Host of the camera",
        examples=["*************"],
    )


class Alert(BaseModel):
    labels: Labels = Field(description="Labels for the alert")
    status: Literal["firing", "resolved"] = Field(description="Status of the alert")


class SNSMessage(BaseModel):
    """A Pydantic model for the SNS message."""

    alerts: list[Alert] = Field(
        default_factory=list,
        description="List of alerts",
    )


# ========== DynamoDB Model ==========
class Model(PynamoModel):
    """Base class for PynamoDB models."""

    def delete(
        self, condition: Condition | None = None, *, add_version_condition: bool = True
    ) -> Any:
        """Delete this object from DynamoDB.

        Overrides the default delete method to add the ability to
        return the old value of the item (ALL_OLD).
        """
        hk_value, rk_value = self._get_hash_range_key_serialized_values()

        version_condition = self._handle_version_attribute()
        if add_version_condition and version_condition is not None:  # pragma: no cover
            condition &= version_condition

        deleted_item = self._get_connection().delete_item(
            hk_value, range_key=rk_value, condition=condition, return_values="ALL_OLD"
        )
        return dynamo_json_to_object(deleted_item["Attributes"])


class CameraStatus(Model):
    """A PynamoDB model for the camera status table."""

    class Meta:
        table_name = os.environ.get(
            "CAMERA_ALERT_TABLE", "camera-connectivity-alerts-prod"
        )

    # Primary key attributes
    SiteId = UnicodeAttribute(hash_key=True)
    CameraId = UnicodeAttribute(range_key=True)

    # Data attributes
    Status = UnicodeAttribute()
    Host = UnicodeAttribute()
    CreatedAt = UTCDateTimeAttribute(default=datetime.now(tz=UTC))


class _OptionalCameraAlertArgs(TypedDict):
    """Optional arguments for the alert_to_camera_status function."""

    CreatedAt: NotRequired[datetime]


def alert_to_camera_status(
    alert: Alert, **kwargs: Unpack[_OptionalCameraAlertArgs]
) -> CameraStatus:
    """Convert an alert to a CameraStatus object."""
    return CameraStatus(
        SiteId=alert.labels.site_id,
        CameraId=alert.labels.camera_id,
        Status=alert.status,
        Host=alert.labels.host,
        **kwargs,
    )
