from datetime import datetime, timedelta
from typing import Any

from boto3.dynamodb.types import TypeDeserializer

deserializer = TypeDeserializer()


def dynamo_json_to_object(json: dict[str, Any]) -> dict[str, Any]:
    """Deserialize a DynamoDB JSON object to a Python object."""
    return {k: deserializer.deserialize(v) for k, v in json.items()}


def format_timedelta(delta: timedelta) -> str:
    total_seconds = int(delta.total_seconds())
    days, remainder = divmod(total_seconds, 86400)
    hours, remainder = divmod(remainder, 3600)
    minutes = remainder // 60

    if days > 0:
        return f"{days}d {hours}h {minutes}m"
    if hours > 0:
        return f"{hours}h {minutes}m"
    return f"{minutes}m"


def create_eid(site_id: str, camera_id: str, created_at: datetime) -> str:
    """Create a unique EID for the support case.

    This EID is used to identify the support case in Netsuite.

    """
    return f"{site_id}_{camera_id}_{int(created_at.timestamp())}"
