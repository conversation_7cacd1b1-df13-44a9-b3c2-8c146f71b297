import os
from dataclasses import dataclass, fields
from typing import Any, TypedDict, Unpack

from netsuite.client import NetSuiteRestApi


@dataclass
class SupportCaseField:
    name: str
    ref_name: str | None = None
    id: str | None = None

    def __post_init__(self) -> None:
        if self.ref_name is None and self.id is None:
            raise ValueError("Either ref_name or id must be set")

    def to_dict(self) -> dict[str, Any]:
        if self.ref_name is not None:
            return {"refName": self.ref_name}

        return {"id": self.id}


@dataclass
class SupportCase:
    eid: str
    title: str | None = None
    emails: list[str] | None = None
    incoming_message: str | None = None
    site_id: str | None = None

    company: SupportCaseField | None = None
    status: SupportCaseField | None = None
    origin: SupportCaseField | None = None
    category: SupportCaseField | None = None
    priority: SupportCaseField | None = None
    product: SupportCaseField | None = None

    def to_payload(self) -> dict[str, Any]:
        body: dict[str, Any] = {}

        if self.title is not None:
            body["title"] = self.title

        if self.emails is not None:
            body["email"] = ", ".join(self.emails)

        if self.incoming_message is not None:
            # difference in production Netsuite form
            is_production = os.environ.get("ENVIRONMENT", "").lower() == "prod"
            body["custevent_parker_2020" if is_production else "incomingMessage"] = self.incoming_message

        if self.site_id is not None:
            body["custevent_f5_site_id"] = self.site_id

        for f in fields(self):
            value = getattr(self, f.name)
            if isinstance(value, SupportCaseField):
                body[value.name] = value.to_dict()

        return body


class _SupportCaseFields(TypedDict, total=False):
    title: str
    emails: list[str]
    incoming_message: str
    site_id: str
    company: SupportCaseField
    status: SupportCaseField
    origin: SupportCaseField
    category: SupportCaseField
    priority: SupportCaseField
    product: SupportCaseField


async def create_support_case(ns: NetSuiteRestApi, case: SupportCase, /) -> None:
    required = (
        "eid",
        "title",
        "company",
        "origin",
        "status",
        "category",
        "priority",
        "site_id",
    )
    missing = [name for name in required if getattr(case, name) is None]
    if missing:
        raise ValueError(f"Missing mandatory fields: {', '.join(missing)}")

    await ns.put(
        f"/record/v1/supportCase/eid:{case.eid}",
        json=case.to_payload(),
    )


async def get_support_case(ns: NetSuiteRestApi, eid: str) -> dict[str, Any]:
    return await ns.get(f"/record/v1/supportCase/eid:{eid}")  # type: ignore[no-any-return]


async def update_support_case(
    ns: NetSuiteRestApi, eid: str, **fields: Unpack[_SupportCaseFields]
) -> None:
    payload = SupportCase(eid=eid, **fields).to_payload()
    await ns.patch(
        f"/record/v1/supportCase/eid:{eid}",
        json=payload,
    )
