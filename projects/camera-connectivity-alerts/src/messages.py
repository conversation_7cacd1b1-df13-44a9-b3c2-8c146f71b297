from datetime import timedelta

from src.models import CameraStatus
from src.utils import format_timedelta


def netsuite_message() -> str:
    """Generate a Netsuite message for a camera outage."""
    return """\
Hi Team,

Our proactive monitoring system has detected that one of the IP cameras used by Eyecue to power your drive-through dashboards is currently disconnected.

Could you please check if the camera is securely connected to the network switch and receiving power? Until it's reconnected, you may notice gaps or inconsistencies in vehicle detection on your in-store dashboard.

If the issue persists after checking the connections, please let us know, and we'll be happy to schedule a time to assist further.

Kind regards,
The Eyecue Team

"""  # noqa: E501


def slack_offline_message(
    camera_status: CameraStatus,
    netsuite_account: str,
    ticket_id: str,
) -> str:
    """Generate a Slack message for an offline camera."""
    netsuite_url = _get_ticket_url(
        netsuite_account=netsuite_account,
        ticket_id=ticket_id,
    )

    return (
        f":orange_alert: Camera *{camera_status.CameraId}* (`{camera_status.Host}`) "
        f"at site `{camera_status.SiteId}` has gone offline. "
        f"View the ticket here: <{netsuite_url}|NetSuite Ticket>"
    )


def slack_online_message(
    camera_status: CameraStatus,
    duration: timedelta,
    netsuite_account: str,
    ticket_id: str,
) -> str:
    """Generate a Slack message for an online camera."""
    duration_str = format_timedelta(duration)
    netsuite_url = _get_ticket_url(
        netsuite_account=netsuite_account,
        ticket_id=ticket_id,
    )

    return (
        f":green-alert: Camera *{camera_status.CameraId}* (`{camera_status.Host}`) at "
        f"site `{camera_status.SiteId}` is back online after {duration_str}. "
        f"View the ticket here: <{netsuite_url}|NetSuite Ticket>"
    )


def _get_ticket_url(
    netsuite_account: str,
    ticket_id: str,
) -> str:
    """Generate a Netsuite ticket URL.

    E.g: https://4818174-sb1.app.netsuite.com/app/crm/support/supportcase.nl?id=122511
    """
    return (
        f"https://{netsuite_account}.app.netsuite.com/app/crm/support/supportcase.nl"
        f"?id={ticket_id}"
    )
